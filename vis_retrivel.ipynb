{"cells": [{"cell_type": "markdown", "id": "fa376b84", "metadata": {}, "source": ["# MBEIR Evaluation and Visualization Notebook\n", "\n", "This notebook evaluates the MBEIR model using recall@k (hit-rate) and provides visualization for query and candidate images. \n", "\n", "## Table of Contents\n", "1. [Load Data](#1-load-data)\n", "2. [Simulate Retrieval](#2-simulate-retrieval-placeholder)\n", "3. [<PERSON><PERSON> Recall@K](#3-calculate-recallat-k-hit-rate)\n", "4. [Visualize Candidate Images](#4-visualize-candidate-images-for-a-query)\n", "5. [Test Visualization](#5-test-visualization)\n", "\n", "### Configuration\n", "Adjust the following paths as necessary for your environment:\n", "- `BASE_DIR`: Root of your LamRA project or where MBEIR data is stored.\n", "- `IMAGE_PATH_PREFIX`: Base directory for resolving image paths from the JSONL files.\n", "\n", "After configuring the paths, run all cells to evaluate the model and visualize results."]}, {"cell_type": "code", "execution_count": null, "id": "cc078623", "metadata": {}, "outputs": [], "source": ["import json\n", "from transformers import AutoProcessor\n", "import sys \n", "import os \n", "current_file_path = os.path.dirname(os.path.abspath(__file__))\n", "module_path = os.path.join(current_file_path, \"../\")\n", "sys.path.append(module_path)\n", "from models.qwen2_vl import Qwen2VLRetForConditionalGeneration\n", "import torch \n", "import argparse\n", "from dataset.datasets_mbeir import QueryDataset, CandidateDataset\n", "from collators.mbeir_eval import MbeirQueryDataCollator, MbeirCandidateDataCollator\n", "from torch.utils.data import DataLoader \n", "import torch.nn.functional as F \n", "from accelerate import Accelerator\n", "import accelerate\n", "\n", "DATASET_QUERY_NUM_UPPER_BOUND = 500000\n", "DATASET_CAN_NUM_UPPER_BOUND = 10000000\n", "\n", "NUM_QUERIES = 70\n", "NUM_CANDIDATES_FOR_POOL = 1000 # Number of candidates to use in the evaluation pool for simulated retrieval\n", "MAX_RETRIEVED_PER_QUERY = 50 # For simulated retrieval and recall calculation up to K=50\n"]}, {"cell_type": "code", "execution_count": null, "id": "2ecde0aa", "metadata": {}, "outputs": [], "source": ["\n", "def unhash_qid(hashed_qid):\n", "    dataset_id = hashed_qid // DATASET_QUERY_NUM_UPPER_BOUND\n", "    data_within_id = hashed_qid % DATASET_QUERY_NUM_UPPER_BOUND\n", "    return f\"{dataset_id}:{data_within_id}\"\n", "\n", "def unhash_did(hashed_did):\n", "    dataset_id = hashed_did // DATASET_CAN_NUM_UPPER_BOUND\n", "    data_within_id = hashed_did % DATASET_CAN_NUM_UPPER_BOUND\n", "    return f\"{dataset_id}:{data_within_id}\"\n", "\n", "def load_qrel(filename):\n", "    qrel = {}\n", "    qid_to_taskid = {}\n", "    with open(filename, \"r\") as f:\n", "        for line in f:\n", "            query_id, _, doc_id, relevance_score, task_id = line.strip().split()\n", "            if int(relevance_score) > 0:  # Assuming only positive relevance scores indicate relevant documents\n", "                if query_id not in qrel:\n", "                    qrel[query_id] = []\n", "                qrel[query_id].append(doc_id)\n", "                if query_id not in qid_to_taskid:\n", "                    qid_to_taskid[query_id] = task_id\n", "    print(f\"Retriever: Loaded {len(qrel)} queries from {filename}\")\n", "    print(\n", "        f\"Retriever: Average number of relevant documents per query: {sum(len(v) for v in qrel.values()) / len(qrel):.2f}\"\n", "    )\n", "    return qrel, qid_to_taskid\n", "\n", "def compute_recall_at_k(relevant_docs, retrieved_indices, k):\n", "    if not relevant_docs:\n", "        return 0.0 # Return 0 if there are no relevant documents\n", "\n", "    # Get the set of indices for the top k retrieved documents\n", "    top_k_retrieved_indices_set = set(retrieved_indices[:k])\n", "\n", "    # Convert the relevant documents to a set\n", "    relevant_docs_set = set(relevant_docs)\n", "\n", "    # Check if there is an intersection between relevant docs and top k retrieved docs\n", "    # If there is, we return 1, indicating successful retrieval; otherwise, we return 0\n", "    if relevant_docs_set.intersection(top_k_retrieved_indices_set):\n", "        return 1.0\n", "    else:\n", "        return 0.0\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "996a17f4", "metadata": {}, "outputs": [], "source": ["\n", "class Args:\n", "    def __init__(self):\n", "        # Define the environment variables from the command\n", "        _MODEL_ID = \"./checkpoints/LamRA-Ret\"\n", "        _ORIGINAL_MODEL_ID = \"Qwen/Qwen2-VL-7B-Instruct\"\n", "        _IMAGE_PATH_PREFIX = \"/mnt/tidal-alsh01/dataset/mmeb/M-BEIR\"\n", "\n", "        # Arguments passed in the command line\n", "        self.query_data_path: str = f\"{_IMAGE_PATH_PREFIX}/query/test/mbeir_xhs_task7_test.jsonl\"\n", "        self.query_cand_pool_path: str = f\"{_IMAGE_PATH_PREFIX}/cand_pool/local/mbeir_xhs_task7_cand_pool.jsonl\"\n", "        self.cand_pool_path: str = f\"{_IMAGE_PATH_PREFIX}/cand_pool/local/mbeir_xhs_task7_cand_pool.jsonl\"\n", "        self.instructions_path: str = f\"{_IMAGE_PATH_PREFIX}/instructions/query_instructions.tsv\"\n", "        self.qrels_path: str = f\"{_IMAGE_PATH_PREFIX}/qrels/test/mbeir_xhs_task7_test_qrels.txt\"\n", "        self.original_model_id: str = _ORIGINAL_MODEL_ID\n", "        self.image_path_prefix: str = _IMAGE_PATH_PREFIX\n", "        self.model_id: str = _MODEL_ID\n", "\n", "        # Argument with a default value from the argparse definition (not overridden in the command)\n", "        self.model_max_length: int = 1024"]}, {"cell_type": "code", "execution_count": null, "id": "fcc3681b", "metadata": {}, "outputs": [], "source": ["args = Args()\n", "\n", "original_model_id = args.original_model_id\n", "model_id = args.model_id \n", "model = Qwen2VLRetForConditionalGeneration.from_pretrained(\n", "    model_id, \n", "    torch_dtype=torch.bfloat16, \n", "    low_cpu_mem_usage=True, \n", ")\n", "\n", "# processor is not changed so we still load from the original model repo\n", "processor = AutoProcessor.from_pretrained(original_model_id)\n", "\n", "tokenizer = processor.tokenizer \n", "tokenizer.model_max_length = args.model_max_length\n", "\n", "def add_embed_token(tokenizer, model, emb_token=\"<emb>\"):\n", "    emb_tokens = [emb_token]\n", "    num_new_tokens = tokenizer.add_tokens(emb_tokens)\n", "    assert len(emb_tokens) == num_new_tokens\n", "\n", "    model.resize_token_embeddings(len(tokenizer))\n", "\n", "    emb_token_ids = tokenizer.convert_tokens_to_ids(emb_tokens)\n", "    model.config.emb_token_ids = emb_token_ids\n", "\n", "add_embed_token(tokenizer, model)\n", "\n", "query_dataset = QueryDataset(\n", "    query_data_path=args.query_data_path, \n", "    cand_pool_path=args.query_cand_pool_path,\n", "    instructions_path=args.instructions_path,\n", "    image_path_prefix=args.image_path_prefix\n", ")\n", "\n", "cand_dataset = CandidateDataset(\n", "    query_data_path=args.query_data_path, \n", "    cand_pool_path=args.cand_pool_path,\n", "    instructions_path=args.instructions_path,\n", "    image_path_prefix=args.image_path_prefix\n", ")\n", "\n", "query_data_collator = MbeirQueryDataCollator(tokenizer=tokenizer, processor=processor)\n", "cand_data_collator = MbeirCandidateDataCollator(tokenizer=tokenizer, processor=processor)\n", "\n", "query_dataloader = DataLoader(query_dataset, batch_size=16, num_workers=8, shuffle=False, collate_fn=query_data_collator)\n", "candidate_dataloader = DataLoader(cand_dataset, batch_size=16, num_workers=8, shuffle=False, collate_fn=cand_data_collator)\n", "\n", "accelerator = Accelerator(mixed_precision='bf16')\n", "device = accelerator.device \n", "is_main_process = accelerator.is_main_process\n", "\n", "model.eval()\n", "\n", "def tensors_to_device(data, device, dtype=model.dtype):\n", "    for key in data.keys():\n", "        if isinstance(data[key], torch.Tensor):\n", "            if key == 'pixel_values':\n", "                data[key] = data[key].to(device).to(dtype)\n", "            else:\n", "                data[key] = data[key].to(device)\n", "    return data \n", "\n", "query_features = []\n", "query_ids = []\n", "candidate_features = []\n", "candidate_ids = []\n", "\n", "cbatch = []\n", "qbatch = []\n", "\n", "from tqdm import tqdm \n", "with torch.no_grad():\n", "    query_dataloader, candidate_dataloader, model = accelerator.prepare(query_dataloader, candidate_dataloader, model)\n", "\n", "    for batch in tqdm(candidate_dataloader, disable=not is_main_process):\n", "        cbatch.append(batch)\n", "        batch = tensors_to_device(batch, device)\n", "        candidate_embed, _, batch_candidate_ids = model(**batch, inference=True)\n", "        candidate_embed = F.normalize(candidate_embed, dim=-1)\n", "        candidate_embed = accelerator.gather_for_metrics(candidate_embed)\n", "        batch_candidate_ids = accelerator.gather_for_metrics(batch_candidate_ids)[:len(candidate_embed)]\n", "        candidate_ids.extend(batch_candidate_ids)\n", "        candidate_features.append(candidate_embed)\n", "        if len(candidate_ids) >= NUM_CANDIDATES_FOR_POOL:\n", "            break\n", "\n", "    for batch in tqdm(query_dataloader, disable=not is_main_process):\n", "        qbatch.append(batch)\n", "        batch = tensors_to_device(batch, device)\n", "        query_embed, batch_query_ids, _ = model(**batch, inference=True)\n", "        query_embed = F.normalize(query_embed, dim=-1)\n", "        query_embed = accelerator.gather_for_metrics(query_embed)\n", "        batch_query_ids = accelerate.utils.gather_object(batch_query_ids)[:len(query_embed)]\n", "        query_ids.extend(batch_query_ids)\n", "        query_features.append(query_embed)\n", "        if len(query_ids) >= NUM_QUERIES:\n", "            break\n", "\n", "query_features = torch.cat(query_features, dim=0)\n", "candidate_features = torch.cat(candidate_features, dim=0)\n"]}, {"cell_type": "code", "execution_count": null, "id": "925f1488", "metadata": {}, "outputs": [], "source": ["\n", "if is_main_process:\n", "    # Adjust the order according to ids \n", "    import numpy as np \n", "\n", "    index = []\n", "    scores = []\n", "    for i in range(len(query_features)):\n", "        query_feature = query_features[i:i+1]\n", "        score = query_feature @ candidate_features.T # (1, num_candidate)\n", "        topk_score, topk_indexes = torch.topk(score, k=50, dim=-1)\n", "        topk_indexes = topk_indexes.squeeze().tolist()\n", "        index.append(topk_indexes)\n", "        scores.append(topk_score.tolist())\n", "\n", "    cand_names = np.array([[unhash_did(candidate_ids[item]) for item in row] for row in index])\n", "    query_names = [unhash_qid(item) for item in query_ids]\n", "\n", "    qrel, qid_to_taskid = load_qrel(args.qrels_path)\n", "\n", "    k_lists = [1, 5, 10, 50]\n", "    res = {}\n", "\n", "    for k in k_lists:\n", "        res[f'recall_{k}'] = []\n", "\n", "    for ind, query_name in enumerate(tqdm(query_names)):\n", "        relevant_docs = qrel[query_name]\n", "        retrieved_indices_for_qid = cand_names[ind]\n", "        for k in k_lists:\n", "            recall_at_k = compute_recall_at_k(relevant_docs, retrieved_indices_for_qid, k)\n", "            res[f'recall_{k}'].append(recall_at_k)\n", "\n", "    for k in k_lists:\n", "        print(f\"recall_at_{k} = {sum(res[f'recall_{k}']) / len(res[f'recall_{k}'])}\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "32c7ad25", "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}