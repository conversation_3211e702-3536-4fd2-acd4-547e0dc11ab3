import os
import sys
current_file_path = os.path.dirname(os.path.abspath(__file__))
module_path = os.path.join(current_file_path, "../")
sys.path.append(module_path)
from dataclasses import asdict
import math
from pathlib import Path
from typing import List, Optional
import yaml
import debugpy

import torch
import transformers
from transformers import Trainer
from arguments import ModelArguments, DataArguments, TrainingArguments, LoraArguments
from collators import COLLATORS
from dataset.datasets_mbeir import LazySupervisedDataset
from dataset.datasets_xhs import XHSDataset
from loaders import LOADERS
from supported_models import MODULE_KEYWORDS
from utils import (
    rank0_print, safe_save_model_for_hf_trainer
)


class SelectiveEmbeddingTrainer(Trainer):
    """Custom trainer that only updates new embedding tokens, not original LLM embeddings."""

    def training_step(self, model, inputs):
        """
        Perform a training step, but zero out gradients for original embeddings.
        """
        model.train()
        inputs = self._prepare_inputs(inputs)

        with self.compute_loss_context_manager():
            loss = self.compute_loss(model, inputs)

        if self.args.n_gpu > 1:
            loss = loss.mean()  # mean() to average on multi-gpu parallel training

        if self.use_apex:
            try:
                from apex import amp
                with amp.scale_loss(loss, self.optimizer) as scaled_loss:
                    scaled_loss.backward()
            except ImportError:
                self.accelerator.backward(loss)
        else:
            self.accelerator.backward(loss)

        # Custom logic: zero out gradients for original embedding tokens
        if hasattr(model.model.embed_tokens, 'new_token_start_idx'):
            embed_weight = model.model.embed_tokens.weight
            if embed_weight.grad is not None:
                # Zero out gradients for original tokens (before new_token_start_idx)
                start_idx = model.model.embed_tokens.new_token_start_idx
                embed_weight.grad[:start_idx] = 0.0

        return loss.detach() / self.args.gradient_accumulation_steps

def setup_debugpy(local_rank):
    import torch.distributed as dist
    if dist.get_rank() == local_rank:
        print(f"Debugger listening on rank {local_rank}")
        debugpy.listen(("0.0.0.0", 9999))
        print("Waiting for debugger attach...")
        debugpy.wait_for_client()
    dist.barrier()

def train():
    parser = transformers.HfArgumentParser(
        (ModelArguments, DataArguments, TrainingArguments, LoraArguments)
    )
    model_args, data_args, training_args, lora_args = parser.parse_args_into_dataclasses()

    # dumping arguments
    output_dir = getattr(training_args, 'output_dir', None)
    assert output_dir is not None, "output_dir is required"
    args_dir = Path(output_dir) / "arguments"
    args_dir.mkdir(parents=True, exist_ok=True)
    yaml.dump(asdict(model_args), open(args_dir / "model.yaml", "w"))
    yaml.dump(asdict(data_args), open(args_dir / "data.yaml", "w"))
    yaml.dump(asdict(training_args), open(args_dir / "training.yaml", "w"))
    yaml.dump(asdict(lora_args), open(args_dir / "lora.yaml", "w"))  # Keep for compatibility

    # Warning about LoRA being disabled
    rank0_print("WARNING: LoRA arguments are ignored. Training with selective freezing instead.")
    rank0_print("- LLM backbone will be frozen")
    rank0_print("- Only emb_head and newly added embeddings will be trained")

    compute_dtype = (torch.float16 if training_args.fp16 else (torch.bfloat16 if training_args.bf16 else torch.float32))

    # No LoRA or quantization - using full precision training for selected components
    device_map = None
    
    # load model, tokenizer, processor
    rank0_print("Loading model, tokenizer, processor...")
    loader = LOADERS[model_args.model_family_id](
        model_hf_path=model_args.model_hf_path,
        model_local_path=model_args.model_local_path,
        compute_dtype=compute_dtype,
        bnb_config=None,
        use_flash_attn=training_args.use_flash_attn,
        device_map=device_map,
    )
    model, tokenizer, processor = loader.load(pretrain=False)
    tokenizer.model_max_length = training_args.model_max_length

    # Set language loss weight from training arguments
    model.config.language_loss_weight = training_args.language_loss_weight

    if training_args.gradient_checkpointing:
        model.enable_input_require_grads()

    # freeze certain params
    vision_encoder_keys = MODULE_KEYWORDS[model_args.model_family_id]["vision_encoder"]
    if not training_args.train_vision_encoder:
        rank0_print(f"Vision encoder is freezed... including:")
        for module in vision_encoder_keys:
            rank0_print(f"\t{module}")
            eval(f"model.{module}").requires_grad_(False)

    vision_projector_keys = MODULE_KEYWORDS[model_args.model_family_id]["vision_projector"]
    if not training_args.train_vision_projector:
        rank0_print(f"Vision projector is freezed... including:")
        for module in vision_projector_keys:
            rank0_print(f"\t{module}")
            eval(f"model.{module}").requires_grad_(False)

    # other components preparation (e.g., image_newline, vision_resampler)
    # we will just freeze these
    if "others" in MODULE_KEYWORDS[model_args.model_family_id]:
        rank0_print(f"Other multimodal component is freezed... including:")
        for other_key in MODULE_KEYWORDS[model_args.model_family_id]["others"]:
            rank0_print(f"\t{other_key}")
            eval(f"model.{other_key}").requires_grad_(False)

    # Freeze LLM backbone and selectively train components
    llm_keys = MODULE_KEYWORDS[model_args.model_family_id]["llm"]
    llm_heads_keys = MODULE_KEYWORDS[model_args.model_family_id]["llm_heads"]

    # Freeze the entire LLM backbone
    rank0_print("Freezing LLM backbone...")
    for module_key in llm_keys:
        rank0_print(f"\tFreezing {module_key}")
        eval(f"model.{module_key}").requires_grad_(False)

    # Make embedding head trainable for contrastive learning
    rank0_print("Embedding head will be fully trained...")
    for head_key in llm_heads_keys:
        if hasattr(model, head_key.split('.')[-1]):  # Check if the head exists
            rank0_print(f"\tTraining {head_key}")
            eval(f"model.{head_key}").requires_grad_(True)

    # Make only the newly added embedding tokens trainable (not original LLM embeddings)
    if hasattr(model.config, 'emb_token_ids') and model.config.emb_token_ids is not None:
        rank0_print("Making only newly added embedding tokens trainable...")

        # Calculate the indices for new tokens
        total_vocab_size = model.model.embed_tokens.weight.shape[0]
        new_token_start_idx = total_vocab_size - len(model.config.emb_token_ids)

        rank0_print(f"\tTotal vocab size: {total_vocab_size}")
        rank0_print(f"\tNew tokens start at index: {new_token_start_idx}")
        rank0_print(f"\tNumber of new tokens: {len(model.config.emb_token_ids)}")

        # Store the indices for later use in custom training step
        model.model.embed_tokens.new_token_start_idx = new_token_start_idx
        model.model.embed_tokens.new_token_end_idx = total_vocab_size

        rank0_print("\tNew embedding tokens will be selectively trained")

    rank0_print("No LoRA enabled - using selective freezing instead")
        
    # print trainable parameters for inspection
    rank0_print("Trainable parameters:")
    for name, param in model.named_parameters():
        if param.requires_grad:
            rank0_print(f"\t{name}")

    # load data
    rank0_print("Loading data...")
    mbeir_dataset = LazySupervisedDataset(
        query_data_path=data_args.query_data_path,
        cand_pool_path=data_args.cand_pool_path,
        instructions_path=data_args.instructions_path,
        image_path_prefix=data_args.image_path_prefix,
        tokenizer=tokenizer,
    )

    xhs_dataset = XHSDataset(
        query_data_path=data_args.xhs_query_data_path,
        cand_pool_path=data_args.xhs_cand_pool_path,
        instructions_path=data_args.instructions_path,
        image_path_prefix=data_args.image_path_prefix,
        tokenizer=tokenizer 
    )

    train_dataset = torch.utils.data.ConcatDataset([mbeir_dataset, xhs_dataset])

    training_args.eval_strategy = "no"

    # data collator
    data_collator = COLLATORS[model_args.model_family_id](
        tokenizer=tokenizer,
        processor=processor,
    )

    # training_args.gradient_checkpointing_kwargs = {"use_reentrant": False} # add this one
    trainer = SelectiveEmbeddingTrainer(
        model=model,
        args=training_args,
        data_collator=data_collator,
        train_dataset=train_dataset,
    )
    
    trainer.train()
    trainer.save_state()

    safe_save_model_for_hf_trainer(trainer=trainer, output_dir=output_dir)
    

if __name__ == "__main__":
    train()