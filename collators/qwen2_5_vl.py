from typing import Dict, Sequence

import torch

from . import register_collator
from .base import BaseDataCollator
from .qwen2_5_vision_process import process_vision_info


@register_collator("qwen2_5-vl-7b")
class Qwen2_5VLDataCollator(BaseDataCollator):
    @property
    def PAD_TOKEN_ID(self) -> int:
        return self.tokenizer.pad_token_id

    def __call__(self, messages: Sequence[Dict]) -> Dict[str, torch.Tensor]:
        
        category_size = len(messages[0])
        if category_size == 3:
            has_hard_negative = True 
        else:
            has_hard_negative = False 
        
        new_messages = []
        for category in range(category_size):
            for item in messages:
                new_messages.append(item[category])

        texts = [
            self.processor.apply_chat_template(msg, tokenize=False, add_generation_prompt=False)
            for msg in new_messages
        ]
        image_inputs, video_inputs = process_vision_info(new_messages)
        inputs = self.processor(
            text=texts,
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )

        input_ids = inputs['input_ids']
        labels = input_ids.clone()
        labels[labels == self.PAD_TOKEN_ID] = self.IGNORE_TOKEN_ID

        if 'attention_mask' in inputs:
            attention_mask = inputs['attention_mask']
        else:
            attention_mask = None 
        if 'pixel_values' in inputs:
            pixel_values = inputs['pixel_values']
        else:
            pixel_values = None 
        if 'image_grid_thw' in inputs:
            image_grid_thw = inputs['image_grid_thw']
        else:
            image_grid_thw = None 

        image_inputs, video_inputs = process_vision_info(new_messages, box_op="crop-ret-none")
        focal_image_ids = torch.tensor([i for i, e in enumerate(image_inputs) if e is not None])
        image_inputs = [e for e in image_inputs if e is not None]

        focal_inputs = self.processor.image_processor(
            images=image_inputs,
            return_tensors="pt",
        )
        
        focal_pixel_values = focal_inputs.pixel_values
        focal_image_grid_thw = focal_inputs.image_grid_thw

        return dict(
            input_ids=input_ids,
            attention_mask=attention_mask,
            pixel_values=pixel_values,
            image_grid_thw=image_grid_thw,
            labels=labels,
            has_hard_negative=has_hard_negative,
            focal_pixel_values=focal_pixel_values,
            focal_image_grid_thw=focal_image_grid_thw,
            focal_image_ids=focal_image_ids,
        )